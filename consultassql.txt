🌿 Si quieres ver los registros antes de decidir:
SELECT *
FROM streams
WHERE stream_source LIKE '%gotv.vip%';

💣 Si quieres borrarlos (¡pero solo cuando estés 1000% seguro!):
DELETE
FROM streams
WHERE stream_source LIKE '%gotv.vip%';

Si quieres borrar de la tabla streams solamente las filas que tengan:
DELETE FROM streams
WHERE type = 5
  AND stream_source LIKE '%gotv.vip%';

🎯 ¿Qué quieres?
Contar cuántos stream_id en la tabla streams_episodes NO existen en la tabla streams (columna id).
SELECT COUNT(*)
FROM streams_episodes se
LEFT JOIN streams s ON se.stream_id = s.id
WHERE s.id IS NULL;

⚔️ Consulta para BORRAR esos 7,609 registros huérfanos de streams_episodes:

DELETE se
FROM streams_episodes se
LEFT JOIN streams s ON se.stream_id = s.id
WHERE s.id IS NULL;

🔥 Listo, sin vueltas, sin humo y con sabor a verdad pura.
SELECT s.*
FROM streams s
JOIN (
    SELECT stream_display_name
    FROM streams
    WHERE type = 2
    GROUP BY stream_display_name
    HAVING COUNT(*) > 1
) repetidas
ON s.stream_display_name = repetidas.stream_display_name
WHERE s.type = 2
ORDER BY s.stream_display_name, s.id;

🎯 Paso 1: Consulta para ver los repetidos con direct_source
SELECT s.*
FROM streams s
JOIN (
    SELECT stream_display_name
    FROM streams
    WHERE type = 2
      AND direct_source IS NOT NULL
      AND direct_source <> ''
    GROUP BY stream_display_name
    HAVING COUNT(*) > 1
) repetidas
ON s.stream_display_name = repetidas.stream_display_name
WHERE s.type = 2
  AND (s.direct_source IS NOT NULL AND s.direct_source <> '')
ORDER BY s.stream_display_name, s.id;


# SQL Queries - Limpieza de Streams Duplicados

## 🔥 Consulta inicial para ver todos los repetidos
```sql
SELECT s.*
FROM streams s
JOIN (
    SELECT stream_display_name
    FROM streams
    WHERE type = 2
    GROUP BY stream_display_name
    HAVING COUNT(*) > 1
) repetidas
ON s.stream_display_name = repetidas.stream_display_name
WHERE s.type = 2
ORDER BY s.stream_display_name, s.id;
```

## 🎯 Paso 1: Consulta para ver los repetidos con direct_source
```sql
SELECT s.*
FROM streams s
JOIN (
    SELECT stream_display_name
    FROM streams
    WHERE type = 2
      AND direct_source IS NOT NULL
      AND direct_source <> ''
    GROUP BY stream_display_name
    HAVING COUNT(*) > 1
) repetidas
ON s.stream_display_name = repetidas.stream_display_name
WHERE s.type = 2
  AND (s.direct_source IS NOT NULL AND s.direct_source <> '')
ORDER BY s.stream_display_name, s.id;
```

## 🎯 Paso 2: Preparar para borrar

### 🔥 Consulta para ver cuál conservar
```sql
SELECT MIN(id) AS id_a_conservar, stream_display_name
FROM streams
WHERE type = 2
  AND direct_source IS NOT NULL
  AND direct_source <> ''
GROUP BY stream_display_name
HAVING COUNT(*) > 1;
```

### 🔥 Consulta para listar IDs a borrar
```sql
SELECT s.id
FROM streams s
JOIN (
    SELECT stream_display_name, MIN(id) AS id_a_conservar
    FROM streams
    WHERE type = 2
      AND direct_source IS NOT NULL
      AND direct_source <> ''
    GROUP BY stream_display_name
    HAVING COUNT(*) > 1
) t
ON s.stream_display_name = t.stream_display_name
WHERE s.type = 2
  AND (s.direct_source IS NOT NULL AND s.direct_source <> '')
  AND s.id <> t.id_a_conservar;
```

## 💣 Consulta final para borrar
⚠️ ¡Atención! Primero revisa bien los IDs antes de ejecutar.
```sql
DELETE FROM streams
WHERE id IN (
    SELECT borrar.id
    FROM (
        SELECT s.id
        FROM streams s
        JOIN (
            SELECT stream_display_name, MIN(id) AS id_a_conservar
            FROM streams
            WHERE type = 2
              AND direct_source IS NOT NULL
              AND direct_source <> ''
            GROUP BY stream_display_name
            HAVING COUNT(*) > 1
        ) t
        ON s.stream_display_name = t.stream_display_name
        WHERE s.type = 2
          AND (s.direct_source IS NOT NULL AND s.direct_source <> '')
          AND s.id <> t.id_a_conservar
    ) AS borrar
);
```

## 💡 Consulta para ver SOLO los que son direct_source o direct_proxy (sin symlink)
```sql
SELECT s.*
FROM streams s
JOIN (
    SELECT stream_display_name
    FROM streams
    WHERE type = 2
      AND (
        (direct_source IS NOT NULL AND direct_source <> '')
        OR (direct_proxy IS NOT NULL AND direct_proxy <> '')
      )
    GROUP BY stream_display_name
    HAVING COUNT(*) > 1
) repetidas
ON s.stream_display_name = repetidas.stream_display_name
WHERE s.type = 2
  AND (
    (s.direct_source IS NOT NULL AND s.direct_source <> '')
    OR (s.direct_proxy IS NOT NULL AND s.direct_proxy <> '')
  )
ORDER BY s.stream_display_name, s.id;
```

## 💣 Consulta final para borrar (dejando solo uno por grupo)
⚠️ ¡Revisa bien antes de ejecutar!
```sql
DELETE FROM streams
WHERE id IN (
    SELECT borrar.id
    FROM (
        SELECT s.id
        FROM streams s
        JOIN (
            SELECT stream_display_name, MIN(id) AS id_a_conservar
            FROM streams
            WHERE type = 2
              AND (
                (direct_source IS NOT NULL AND direct_source <> '')
                OR (direct_proxy IS NOT NULL AND direct_proxy <> '')
              )
            GROUP BY stream_display_name
            HAVING COUNT(*) > 1
        ) t
        ON s.stream_display_name = t.stream_display_name
        WHERE s.type = 2
          AND (
            (s.direct_source IS NOT NULL AND s.direct_source <> '')
            OR (s.direct_proxy IS NOT NULL AND s.direct_proxy <> '')
          )
          AND s.id <> t.id_a_conservar
    ) AS borrar
);
```

## 💥 Consulta para ver symlinks y direct_source repetidos
```sql
SELECT s.*
FROM streams s
JOIN (
    SELECT stream_display_name
    FROM streams
    WHERE type = 2
      AND (
        (movie_symlink IS NOT NULL AND movie_symlink <> '')
        OR (direct_source IS NOT NULL AND direct_source <> '')
      )
    GROUP BY stream_display_name
    HAVING COUNT(*) > 1
) repetidos
ON s.stream_display_name = repetidos.stream_display_name
WHERE s.type = 2
  AND (
    (s.movie_symlink IS NOT NULL AND s.movie_symlink <> '')
    OR (s.direct_source IS NOT NULL AND s.direct_source <> '')
  )
ORDER BY s.stream_display_name, s.id;
```

## 💥 Consulta para ver los que se van a conservar (uno por grupo)
```sql
SELECT MIN(id) AS id_a_conservar, stream_display_name
FROM streams
WHERE type = 2
  AND (
    (direct_source IS NOT NULL AND direct_source <> '')
    OR (direct_proxy IS NOT NULL AND direct_proxy <> '')
  )
GROUP BY stream_display_name;
```

## 💥 Consulta para ver los IDs que se van a borrar
```sql
SELECT s.id
FROM streams s
JOIN (
    SELECT stream_display_name, MIN(id) AS id_a_conservar
    FROM streams
    WHERE type = 2
      AND (
        (direct_source IS NOT NULL AND direct_source <> '')
        OR (direct_proxy IS NOT NULL AND direct_proxy <> '')
      )
    GROUP BY stream_display_name
) t
ON s.stream_display_name = t.stream_display_name
WHERE s.type = 2
  AND (
    (s.direct_source IS NOT NULL AND s.direct_source <> '')
    OR (s.direct_proxy IS NOT NULL AND s.direct_proxy <> '')
  )
  AND s.id <> t.id_a_conservar;
```

## 💣 Consulta final para borrar duplicados (sin tocar symlinks)
⚠️ ¡Verifica antes de ejecutar!
```sql
DELETE FROM streams
WHERE id IN (
    SELECT borrar.id
    FROM (
        SELECT s.id
        FROM streams s
        JOIN (
            SELECT stream_display_name, MIN(id) AS id_a_conservar
            FROM streams
            WHERE type = 2
              AND (
                (direct_source IS NOT NULL AND direct_source <> '')
                OR (direct_proxy IS NOT NULL AND direct_proxy <> '')
              )
            GROUP BY stream_display_name
        ) t
        ON s.stream_display_name = t.stream_display_name
        WHERE s.type = 2
          AND (
            (s.direct_source IS NOT NULL AND s.direct_source <> '')
            OR (s.direct_proxy IS NOT NULL AND s.direct_proxy <> '')
          )
          AND s.id <> t.id_a_conservar
    ) AS borrar
);
```

## 💥 Consulta para ver qué títulos tienen symlink
```sql
SELECT DISTINCT stream_display_name
FROM streams
WHERE type = 2
  AND movie_symlink IS NOT NULL
  AND movie_symlink <> '';
```

## 💣 Consulta para ver IDs de direct_source y direct_proxy a borrar (si hay symlink en el grupo)
```sql
SELECT s.id
FROM streams s
WHERE s.type = 2
  AND (
    (s.direct_source IS NOT NULL AND s.direct_source <> '')
    OR (s.direct_proxy IS NOT NULL AND s.direct_proxy <> '')
  )
  AND s.stream_display_name IN (
    SELECT stream_display_name
    FROM streams
    WHERE type = 2
      AND movie_symlink IS NOT NULL
      AND movie_symlink <> ''
  );
```

## 💣 Consulta final para borrar
⚠️ ¡Verifica antes de ejecutar!
```sql
DELETE FROM streams
WHERE id IN (
    SELECT borrar.id
    FROM (
        SELECT s.id
        FROM streams s
        WHERE s.type = 2
          AND (
            (s.direct_source IS NOT NULL AND s.direct_source <> '')
            OR (s.direct_proxy IS NOT NULL AND s.direct_proxy <> '')
          )
          AND s.stream_display_name IN (
            SELECT stream_display_name
            FROM streams
            WHERE type = 2
              AND movie_symlink IS NOT NULL
              AND movie_symlink <> ''
          )
    ) AS borrar
);
```

## 🌾 Resumen conservador y limpio
✅ Symlinks se quedan, intocables como una espada sagrada.
✅ Si hay symlink, volamos todos los direct_source y direct_proxy del mismo título.
✅ Si no hay symlink, no se hace nada.



necesito localizar series con episodios  incompletos, el patron arranca aca 
la tabla streams_series tiene las columnas id title category_id seasons, esto conecta con la tabla streams_episodes que ve las columnas id season_num episode_num series_id stream_id y esto conecta con la tabla streams con las columnas id stream_display_name, la columna type en esta misma tabla conecta con streams_types con la columna type_id 5 que la identifica como series.


SELECT 
    ss.id AS series_id,
    ss.title,
    se.season_num,
    MIN(se.episode_num) AS primer_ep,
    MAX(se.episode_num) AS ultimo_ep,
    COUNT(se.episode_num) AS episodios_disponibles,
    (MAX(se.episode_num) - MIN(se.episode_num) + 1) AS episodios_esperados,
    ((MAX(se.episode_num) - MIN(se.episode_num) + 1) - COUNT(se.episode_num)) AS faltantes
FROM streams_series ss
JOIN streams_episodes se ON ss.id = se.series_id
JOIN streams s ON se.stream_id = s.id
WHERE s.type = 5
GROUP BY ss.id, se.season_num
HAVING faltantes > 0
ORDER BY ss.title, se.season_num;
