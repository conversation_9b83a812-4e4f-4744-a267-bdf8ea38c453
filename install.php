<?php
/**
 * Instalador para XUI Admin Panel
 * Script de instalación y configuración inicial
 */

// Verificar si ya está instalado
if (file_exists('config.php')) {
    $config_exists = true;
    require_once 'config.php';
} else {
    $config_exists = false;
}

$step = $_GET['step'] ?? 1;
$errors = [];
$success_messages = [];

// Procesar formularios
if ($_POST) {
    switch ($step) {
        case 2:
            $errors = processStep2();
            if (empty($errors)) {
                header('Location: install.php?step=3');
                exit;
            }
            break;
        case 3:
            $errors = processStep3();
            if (empty($errors)) {
                header('Location: install.php?step=4');
                exit;
            }
            break;
    }
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instalación - XUI Admin Panel</title>
    <link rel="stylesheet" href="assets/css/xbox-style.css">
    <link rel="stylesheet" href="assets/css/main.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .install-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .install-step {
            background: var(--xbox-dark-gray);
            border-radius: 12px;
            padding: 2rem;
            border: 2px solid var(--xbox-green);
            margin-bottom: 2rem;
        }
        .step-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--xbox-green);
        }
        .step-number {
            background: var(--xbox-green);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .form-full {
            grid-column: 1 / -1;
        }
        .error-message {
            background: var(--xbox-error);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .success-message {
            background: var(--xbox-green);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .requirements-list {
            list-style: none;
            padding: 0;
        }
        .requirements-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--xbox-light-gray);
        }
        .req-ok { color: var(--xbox-green); }
        .req-error { color: var(--xbox-error); }
        .req-warning { color: var(--xbox-warning); }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="step-header">
            <h1><i class="fas fa-gamepad"></i> XUI Admin Panel</h1>
            <p>Instalación y Configuración</p>
        </div>

        <?php if (!empty($errors)): ?>
            <div class="error-message">
                <h4><i class="fas fa-exclamation-triangle"></i> Errores encontrados:</h4>
                <ul>
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if (!empty($success_messages)): ?>
            <div class="success-message">
                <?php foreach ($success_messages as $message): ?>
                    <p><i class="fas fa-check"></i> <?php echo htmlspecialchars($message); ?></p>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <?php if ($step == 1): ?>
            <!-- Paso 1: Verificar Requisitos -->
            <div class="install-step">
                <h2><span class="step-number">1</span>Verificar Requisitos</h2>
                
                <ul class="requirements-list">
                    <li>
                        <i class="fas fa-<?php echo version_compare(PHP_VERSION, '7.4.0', '>=') ? 'check req-ok' : 'times req-error'; ?>"></i>
                        PHP 7.4+ (Actual: <?php echo PHP_VERSION; ?>)
                    </li>
                    <li>
                        <i class="fas fa-<?php echo extension_loaded('pdo') ? 'check req-ok' : 'times req-error'; ?>"></i>
                        Extensión PDO
                    </li>
                    <li>
                        <i class="fas fa-<?php echo extension_loaded('pdo_mysql') ? 'check req-ok' : 'times req-error'; ?>"></i>
                        Extensión PDO MySQL
                    </li>
                    <li>
                        <i class="fas fa-<?php echo is_writable('.') ? 'check req-ok' : 'times req-error'; ?>"></i>
                        Directorio escribible
                    </li>
                    <li>
                        <i class="fas fa-<?php echo function_exists('curl_init') ? 'check req-ok' : 'exclamation-triangle req-warning'; ?>"></i>
                        cURL (recomendado)
                    </li>
                </ul>

                <div style="text-align: center; margin-top: 2rem;">
                    <a href="install.php?step=2" class="xbox-btn primary">
                        <i class="fas fa-arrow-right"></i> Continuar
                    </a>
                </div>
            </div>

        <?php elseif ($step == 2): ?>
            <!-- Paso 2: Configuración de Base de Datos -->
            <div class="install-step">
                <h2><span class="step-number">2</span>Configuración de Base de Datos</h2>
                
                <form method="POST">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="db_host">Host de Base de Datos:</label>
                            <input type="text" id="db_host" name="db_host" value="<?php echo $_POST['db_host'] ?? 'localhost'; ?>" class="xbox-input" required>
                        </div>
                        <div class="form-group">
                            <label for="db_port">Puerto:</label>
                            <input type="number" id="db_port" name="db_port" value="<?php echo $_POST['db_port'] ?? '3306'; ?>" class="xbox-input">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="db_name">Nombre de Base de Datos:</label>
                            <input type="text" id="db_name" name="db_name" value="<?php echo $_POST['db_name'] ?? 'xtream_iptvpro'; ?>" class="xbox-input" required>
                        </div>
                        <div class="form-group">
                            <label for="db_user">Usuario:</label>
                            <input type="text" id="db_user" name="db_user" value="<?php echo $_POST['db_user'] ?? 'root'; ?>" class="xbox-input" required>
                        </div>
                    </div>
                    
                    <div class="form-group form-full">
                        <label for="db_pass">Contraseña:</label>
                        <input type="password" id="db_pass" name="db_pass" value="<?php echo $_POST['db_pass'] ?? ''; ?>" class="xbox-input">
                    </div>

                    <div style="text-align: center; margin-top: 2rem;">
                        <button type="submit" class="xbox-btn primary">
                            <i class="fas fa-database"></i> Probar Conexión
                        </button>
                    </div>
                </form>
            </div>

        <?php elseif ($step == 3): ?>
            <!-- Paso 3: Configuración de Seguridad -->
            <div class="install-step">
                <h2><span class="step-number">3</span>Configuración de Seguridad</h2>
                
                <form method="POST">
                    <div class="form-group">
                        <label for="admin_password">Contraseña de Administrador:</label>
                        <input type="password" id="admin_password" name="admin_password" class="xbox-input" required minlength="6">
                        <small style="color: var(--xbox-light-gray);">Mínimo 6 caracteres</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password">Confirmar Contraseña:</label>
                        <input type="password" id="confirm_password" name="confirm_password" class="xbox-input" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="session_timeout">Timeout de Sesión (minutos):</label>
                            <input type="number" id="session_timeout" name="session_timeout" value="60" class="xbox-input" min="5" max="1440">
                        </div>
                        <div class="form-group">
                            <label for="app_env">Entorno:</label>
                            <select id="app_env" name="app_env" class="xbox-select">
                                <option value="development">Desarrollo</option>
                                <option value="production">Producción</option>
                            </select>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 2rem;">
                        <button type="submit" class="xbox-btn primary">
                            <i class="fas fa-shield-alt"></i> Configurar Seguridad
                        </button>
                    </div>
                </form>
            </div>

        <?php elseif ($step == 4): ?>
            <!-- Paso 4: Finalización -->
            <div class="install-step">
                <h2><span class="step-number">4</span>¡Instalación Completada!</h2>
                
                <div class="success-message">
                    <h3><i class="fas fa-check-circle"></i> ¡Felicidades!</h3>
                    <p>XUI Admin Panel se ha instalado correctamente.</p>
                </div>

                <h4>Próximos pasos:</h4>
                <ol style="color: var(--xbox-light-gray); line-height: 1.6;">
                    <li>Elimina o renombra el archivo <code>install.php</code> por seguridad</li>
                    <li>Configura los permisos de archivos apropiados</li>
                    <li>Revisa la configuración en <code>config.php</code></li>
                    <li>Accede al panel de administración</li>
                </ol>

                <div style="text-align: center; margin-top: 2rem;">
                    <a href="index.php" class="xbox-btn primary">
                        <i class="fas fa-rocket"></i> Acceder al Panel
                    </a>
                    <a href="README.md" class="xbox-btn secondary" target="_blank">
                        <i class="fas fa-book"></i> Ver Documentación
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>

<?php
/**
 * Procesar paso 2: Configuración de base de datos
 */
function processStep2() {
    $errors = [];
    
    $host = $_POST['db_host'] ?? '';
    $port = $_POST['db_port'] ?? '3306';
    $name = $_POST['db_name'] ?? '';
    $user = $_POST['db_user'] ?? '';
    $pass = $_POST['db_pass'] ?? '';
    
    if (empty($host) || empty($name) || empty($user)) {
        $errors[] = 'Todos los campos son requeridos excepto la contraseña';
        return $errors;
    }
    
    // Probar conexión
    try {
        $dsn = "mysql:host=$host;port=$port;dbname=$name;charset=utf8";
        $pdo = new PDO($dsn, $user, $pass, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        // Verificar que las tablas XUI existen
        $tables = ['streams', 'streams_series', 'streams_episodes'];
        foreach ($tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() == 0) {
                $errors[] = "La tabla '$table' no existe en la base de datos";
            }
        }
        
        if (empty($errors)) {
            // Guardar configuración en sesión
            session_start();
            $_SESSION['install_db'] = [
                'host' => $host,
                'port' => $port,
                'name' => $name,
                'user' => $user,
                'pass' => $pass
            ];
        }
        
    } catch (PDOException $e) {
        $errors[] = 'Error de conexión: ' . $e->getMessage();
    }
    
    return $errors;
}

/**
 * Procesar paso 3: Configuración de seguridad
 */
function processStep3() {
    $errors = [];
    
    $password = $_POST['admin_password'] ?? '';
    $confirm = $_POST['confirm_password'] ?? '';
    $timeout = $_POST['session_timeout'] ?? 60;
    $env = $_POST['app_env'] ?? 'development';
    
    if (strlen($password) < 6) {
        $errors[] = 'La contraseña debe tener al menos 6 caracteres';
    }
    
    if ($password !== $confirm) {
        $errors[] = 'Las contraseñas no coinciden';
    }
    
    if (empty($errors)) {
        session_start();
        $db_config = $_SESSION['install_db'];
        
        // Crear archivo de configuración
        $config_content = generateConfigFile($db_config, $password, $timeout * 60, $env);
        
        if (file_put_contents('config.php', $config_content)) {
            // Crear directorios necesarios
            $dirs = ['logs', 'cache', 'exports', 'backups'];
            foreach ($dirs as $dir) {
                if (!file_exists($dir)) {
                    mkdir($dir, 0755, true);
                }
            }
            
            // Limpiar sesión
            unset($_SESSION['install_db']);
        } else {
            $errors[] = 'No se pudo crear el archivo de configuración';
        }
    }
    
    return $errors;
}

/**
 * Generar archivo de configuración
 */
function generateConfigFile($db, $password, $timeout, $env) {
    return "<?php
// Configuración generada por el instalador
define('DB_HOST', '{$db['host']}');
define('DB_NAME', '{$db['name']}');
define('DB_USER', '{$db['user']}');
define('DB_PASS', '{$db['pass']}');

define('APP_ENV', '$env');
define('APP_DEBUG', " . ($env === 'development' ? 'true' : 'false') . ");
define('ADMIN_PASSWORD', '$password');
define('SESSION_TIMEOUT', $timeout);

define('LOG_ENABLED', true);
define('MAX_RESULTS_PER_PAGE', 1000);
define('MAX_DELETE_BATCH_SIZE', 500);

// Incluir configuración extendida
if (file_exists('config.example.php')) {
    include_once 'config.example.php';
}
?>";
}
?>
