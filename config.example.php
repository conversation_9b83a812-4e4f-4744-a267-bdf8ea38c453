<?php
/**
 * Archivo de configuración de ejemplo para XUI Admin Panel
 * Copia este archivo como config.php y ajusta los valores según tu configuración
 */

// Configuración de Base de Datos
define('DB_HOST', 'localhost');
define('DB_NAME', 'xtream_iptvpro');  // Nombre de tu base de datos XUI
define('DB_USER', 'root');
define('DB_PASS', '');

// Configuración de Entorno
define('APP_ENV', 'development'); // development, production
define('APP_DEBUG', true);

// Configuración de Seguridad
define('ADMIN_PASSWORD', 'admin123'); // Cambia esta contraseña
define('SESSION_TIMEOUT', 3600); // 1 hora en segundos

// Configuración de Logs
define('LOG_ENABLED', true);
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR

// Configuración de Límites
define('MAX_RESULTS_PER_PAGE', 1000);
define('MAX_DELETE_BATCH_SIZE', 500);

// Configuración de Backup (opcional)
define('BACKUP_ENABLED', false);
define('BACKUP_PATH', '/path/to/backups/');

// URLs y Paths
define('BASE_URL', 'http://localhost/xui-admin/');
define('ASSETS_URL', BASE_URL . 'assets/');

// Configuración de Notificaciones
define('NOTIFICATIONS_ENABLED', true);
define('EMAIL_NOTIFICATIONS', false);
define('ADMIN_EMAIL', '<EMAIL>');

// Configuración específica de XUI
$xui_config = [
    'streams_table' => 'streams',
    'series_table' => 'streams_series',
    'episodes_table' => 'streams_episodes',
    'types_table' => 'streams_types',
    
    // Tipos de stream más comunes en XUI
    'stream_types' => [
        1 => 'Live TV',
        2 => 'Movies',
        3 => 'Radio',
        4 => 'Created Live',
        5 => 'Series'
    ],
    
    // Campos importantes para limpieza
    'cleanup_fields' => [
        'direct_source',
        'direct_proxy', 
        'movie_symlink',
        'stream_source'
    ]
];

// Configuración de localización
$localization = [
    'default_language' => 'es',
    'timezone' => 'America/Mexico_City',
    'date_format' => 'Y-m-d H:i:s',
    'number_format' => [
        'decimals' => 0,
        'decimal_separator' => '.',
        'thousands_separator' => ','
    ]
];

// Configuración de rendimiento
$performance_config = [
    'enable_cache' => false,
    'cache_duration' => 300, // 5 minutos
    'enable_compression' => true,
    'max_execution_time' => 300, // 5 minutos
    'memory_limit' => '256M'
];

// Configuración de seguridad avanzada
$security_config = [
    'enable_csrf_protection' => true,
    'enable_rate_limiting' => true,
    'max_requests_per_minute' => 60,
    'enable_ip_whitelist' => false,
    'allowed_ips' => [
        '127.0.0.1',
        '::1'
    ],
    'enable_2fa' => false
];

// Configuración de mantenimiento
$maintenance_config = [
    'auto_cleanup_enabled' => false,
    'auto_cleanup_schedule' => '0 2 * * *', // Cron format: 2 AM daily
    'backup_before_cleanup' => true,
    'max_log_file_size' => '10MB',
    'log_retention_days' => 30
];

// Configuración de API
$api_config = [
    'enable_api' => true,
    'api_key_required' => false,
    'api_rate_limit' => 100, // requests per minute
    'enable_cors' => true,
    'allowed_origins' => ['*']
];

// Configuración de interfaz
$ui_config = [
    'theme' => 'xbox', // xbox, dark, light
    'items_per_page' => 50,
    'enable_animations' => true,
    'show_tooltips' => true,
    'auto_refresh_dashboard' => true,
    'refresh_interval' => 30000 // 30 segundos
];

// Mensajes personalizables
$messages = [
    'welcome' => 'Bienvenido al Panel de Administración XUI',
    'no_results' => 'No se encontraron resultados',
    'confirm_delete' => '¿Estás seguro de que quieres eliminar este elemento?',
    'operation_success' => 'Operación completada exitosamente',
    'operation_error' => 'Error al realizar la operación',
    'connection_error' => 'Error de conexión a la base de datos',
    'unauthorized' => 'No tienes permisos para realizar esta acción'
];

// Configuración de módulos
$modules_config = [
    'streams_management' => true,
    'duplicates_cleanup' => true,
    'series_analysis' => true,
    'database_tools' => true,
    'logs_viewer' => true,
    'backup_restore' => false,
    'user_management' => false,
    'statistics' => true
];

// Configuración de alertas
$alerts_config = [
    'high_duplicates_threshold' => 100,
    'low_disk_space_threshold' => '1GB',
    'high_memory_usage_threshold' => 80, // percentage
    'database_size_threshold' => '10GB'
];

// Configuración de exportación
$export_config = [
    'enable_csv_export' => true,
    'enable_json_export' => true,
    'enable_xml_export' => false,
    'max_export_records' => 10000,
    'export_timeout' => 300 // 5 minutos
];

// Configuración de importación
$import_config = [
    'enable_csv_import' => false,
    'enable_json_import' => false,
    'max_import_file_size' => '50MB',
    'validate_imports' => true,
    'backup_before_import' => true
];

// Configuración de monitoreo
$monitoring_config = [
    'enable_monitoring' => false,
    'monitor_database_size' => true,
    'monitor_query_performance' => true,
    'monitor_error_rates' => true,
    'alert_email' => '<EMAIL>'
];

// Configuración de desarrollo
if (APP_ENV === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
    ini_set('error_log', 'logs/php_errors.log');
}

// Configuración de producción
if (APP_ENV === 'production') {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', 'logs/php_errors.log');
}

// Función para obtener configuración
function getConfig($key = null) {
    global $xui_config, $localization, $performance_config, $security_config, 
           $maintenance_config, $api_config, $ui_config, $messages, 
           $modules_config, $alerts_config, $export_config, $import_config, 
           $monitoring_config;
    
    $config = [
        'xui' => $xui_config,
        'localization' => $localization,
        'performance' => $performance_config,
        'security' => $security_config,
        'maintenance' => $maintenance_config,
        'api' => $api_config,
        'ui' => $ui_config,
        'messages' => $messages,
        'modules' => $modules_config,
        'alerts' => $alerts_config,
        'export' => $export_config,
        'import' => $import_config,
        'monitoring' => $monitoring_config
    ];
    
    if ($key === null) {
        return $config;
    }
    
    return $config[$key] ?? null;
}

// Validar configuración
function validateConfig() {
    $errors = [];
    
    // Validar conexión de base de datos
    if (empty(DB_HOST) || empty(DB_NAME) || empty(DB_USER)) {
        $errors[] = 'Configuración de base de datos incompleta';
    }
    
    // Validar directorios
    if (!is_writable('logs/')) {
        $errors[] = 'El directorio logs/ no es escribible';
    }
    
    // Validar configuración de seguridad
    if (ADMIN_PASSWORD === 'admin123') {
        $errors[] = 'Debes cambiar la contraseña por defecto';
    }
    
    return $errors;
}

// Inicializar configuración
function initConfig() {
    // Crear directorios necesarios
    $dirs = ['logs', 'cache', 'exports', 'backups'];
    foreach ($dirs as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
        }
    }
    
    // Configurar zona horaria
    $localization = getConfig('localization');
    date_default_timezone_set($localization['timezone']);
    
    // Configurar límites de PHP
    $performance = getConfig('performance');
    ini_set('max_execution_time', $performance['max_execution_time']);
    ini_set('memory_limit', $performance['memory_limit']);
}

// Inicializar al cargar el archivo
initConfig();
?>
