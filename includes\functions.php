<?php
/**
 * XUI Admin Panel - Funciones principales
 * Funciones para gestión de streams, duplicados y series
 */

/**
 * Función para sanitizar entrada de usuario
 */
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

/**
 * Función para respuesta JSON
 */
function jsonResponse($data, $success = true, $message = '') {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

/**
 * Función para logging de acciones
 */
function logAction($action, $details = '', $user = 'admin') {
    $logFile = 'logs/admin_actions.log';
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] [$user] $action - $details" . PHP_EOL;
    
    // Crear directorio de logs si no existe
    if (!file_exists('logs')) {
        mkdir('logs', 0755, true);
    }
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * Obtener estadísticas del dashboard
 */
function getDashboardStats($database) {
    try {
        $conn = $database->getConnection();
        $stats = [];
        
        // Total de streams
        $stmt = $conn->query("SELECT COUNT(*) as total FROM streams");
        $stats['total_streams'] = $stmt->fetch()['total'];
        
        // Streams duplicados
        $stmt = $conn->query("
            SELECT COUNT(*) as duplicates 
            FROM (
                SELECT stream_display_name 
                FROM streams 
                WHERE type = 2 
                GROUP BY stream_display_name 
                HAVING COUNT(*) > 1
            ) as dup
        ");
        $stats['duplicate_streams'] = $stmt->fetch()['duplicates'];
        
        // Series incompletas
        $stmt = $conn->query("
            SELECT COUNT(DISTINCT ss.id) as incomplete
            FROM streams_series ss
            JOIN streams_episodes se ON ss.id = se.series_id
            JOIN streams s ON se.stream_id = s.id
            WHERE s.type = 5
            GROUP BY ss.id, se.season_num
            HAVING ((MAX(se.episode_num) - MIN(se.episode_num) + 1) - COUNT(se.episode_num)) > 0
        ");
        $result = $stmt->fetchAll();
        $stats['incomplete_series'] = count($result);
        
        // Streams por tipo
        $stmt = $conn->query("
            SELECT st.type_name, COUNT(s.id) as count
            FROM streams s
            LEFT JOIN streams_types st ON s.type = st.type_id
            GROUP BY s.type, st.type_name
            ORDER BY count DESC
        ");
        $stats['streams_by_type'] = $stmt->fetchAll();
        
        return $stats;
        
    } catch (Exception $e) {
        logAction('ERROR', 'getDashboardStats: ' . $e->getMessage());
        return false;
    }
}

/**
 * Buscar streams por criterios
 */
function searchStreams($database, $filters = []) {
    try {
        $conn = $database->getConnection();
        
        $sql = "
            SELECT s.*, st.type_name 
            FROM streams s 
            LEFT JOIN streams_types st ON s.type = st.type_id 
            WHERE 1=1
        ";
        
        $params = [];
        
        // Filtro por source
        if (!empty($filters['source'])) {
            $sql .= " AND s.stream_source LIKE :source";
            $params[':source'] = '%' . $filters['source'] . '%';
        }
        
        // Filtro por tipo
        if (!empty($filters['type'])) {
            $sql .= " AND s.type = :type";
            $params[':type'] = $filters['type'];
        }
        
        // Filtro por display name
        if (!empty($filters['display_name'])) {
            $sql .= " AND s.stream_display_name LIKE :display_name";
            $params[':display_name'] = '%' . $filters['display_name'] . '%';
        }
        
        $sql .= " ORDER BY s.id DESC LIMIT 1000";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        $results = $stmt->fetchAll();
        
        logAction('SEARCH_STREAMS', 'Filters: ' . json_encode($filters) . ', Results: ' . count($results));
        
        return $results;
        
    } catch (Exception $e) {
        logAction('ERROR', 'searchStreams: ' . $e->getMessage());
        return false;
    }
}

/**
 * Eliminar streams por IDs
 */
function deleteStreams($database, $streamIds, $confirm = false) {
    if (!$confirm) {
        return ['success' => false, 'message' => 'Confirmación requerida'];
    }
    
    try {
        $conn = $database->getConnection();
        $conn->beginTransaction();
        
        // Preparar consulta de eliminación
        $placeholders = str_repeat('?,', count($streamIds) - 1) . '?';
        $sql = "DELETE FROM streams WHERE id IN ($placeholders)";
        
        $stmt = $conn->prepare($sql);
        $result = $stmt->execute($streamIds);
        
        $deletedCount = $stmt->rowCount();
        
        $conn->commit();
        
        logAction('DELETE_STREAMS', 'Deleted ' . $deletedCount . ' streams. IDs: ' . implode(',', $streamIds));
        
        return [
            'success' => true, 
            'message' => "Se eliminaron $deletedCount streams correctamente",
            'deleted_count' => $deletedCount
        ];
        
    } catch (Exception $e) {
        $conn->rollBack();
        logAction('ERROR', 'deleteStreams: ' . $e->getMessage());
        return ['success' => false, 'message' => 'Error al eliminar streams: ' . $e->getMessage()];
    }
}

/**
 * Obtener streams duplicados
 */
function getDuplicateStreams($database, $type = 'all') {
    try {
        $conn = $database->getConnection();
        
        $sql = "
            SELECT s.*
            FROM streams s
            JOIN (
                SELECT stream_display_name
                FROM streams
                WHERE type = 2
        ";
        
        // Filtros adicionales según el tipo de duplicados
        switch ($type) {
            case 'with_direct_source':
                $sql .= " AND direct_source IS NOT NULL AND direct_source <> ''";
                break;
            case 'with_symlink':
                $sql .= " AND movie_symlink IS NOT NULL AND movie_symlink <> ''";
                break;
        }
        
        $sql .= "
                GROUP BY stream_display_name
                HAVING COUNT(*) > 1
            ) repetidas
            ON s.stream_display_name = repetidas.stream_display_name
            WHERE s.type = 2
        ";
        
        // Aplicar mismo filtro en el WHERE
        switch ($type) {
            case 'with_direct_source':
                $sql .= " AND (s.direct_source IS NOT NULL AND s.direct_source <> '')";
                break;
            case 'with_symlink':
                $sql .= " AND (s.movie_symlink IS NOT NULL AND s.movie_symlink <> '')";
                break;
        }
        
        $sql .= " ORDER BY s.stream_display_name, s.id";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll();
        
        logAction('GET_DUPLICATES', 'Type: ' . $type . ', Results: ' . count($results));
        
        return $results;
        
    } catch (Exception $e) {
        logAction('ERROR', 'getDuplicateStreams: ' . $e->getMessage());
        return false;
    }
}

/**
 * Obtener series incompletas
 */
function getIncompleteSeriesData($database) {
    try {
        $conn = $database->getConnection();
        
        $sql = "
            SELECT 
                ss.id AS series_id,
                ss.title,
                se.season_num,
                MIN(se.episode_num) AS primer_ep,
                MAX(se.episode_num) AS ultimo_ep,
                COUNT(se.episode_num) AS episodios_disponibles,
                (MAX(se.episode_num) - MIN(se.episode_num) + 1) AS episodios_esperados,
                ((MAX(se.episode_num) - MIN(se.episode_num) + 1) - COUNT(se.episode_num)) AS faltantes
            FROM streams_series ss
            JOIN streams_episodes se ON ss.id = se.series_id
            JOIN streams s ON se.stream_id = s.id
            WHERE s.type = 5
            GROUP BY ss.id, se.season_num
            HAVING faltantes > 0
            ORDER BY ss.title, se.season_num
        ";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll();
        
        logAction('GET_INCOMPLETE_SERIES', 'Results: ' . count($results));
        
        return $results;
        
    } catch (Exception $e) {
        logAction('ERROR', 'getIncompleteSeriesData: ' . $e->getMessage());
        return false;
    }
}

/**
 * Limpiar registros huérfanos de streams_episodes
 */
function cleanOrphanedEpisodes($database, $confirm = false) {
    if (!$confirm) {
        return ['success' => false, 'message' => 'Confirmación requerida'];
    }
    
    try {
        $conn = $database->getConnection();
        $conn->beginTransaction();
        
        // Primero contar cuántos registros huérfanos hay
        $countSql = "
            SELECT COUNT(*)
            FROM streams_episodes se
            LEFT JOIN streams s ON se.stream_id = s.id
            WHERE s.id IS NULL
        ";
        
        $stmt = $conn->prepare($countSql);
        $stmt->execute();
        $orphanedCount = $stmt->fetchColumn();
        
        if ($orphanedCount > 0) {
            // Eliminar registros huérfanos
            $deleteSql = "
                DELETE se
                FROM streams_episodes se
                LEFT JOIN streams s ON se.stream_id = s.id
                WHERE s.id IS NULL
            ";
            
            $stmt = $conn->prepare($deleteSql);
            $stmt->execute();
            $deletedCount = $stmt->rowCount();
            
            $conn->commit();
            
            logAction('CLEAN_ORPHANED_EPISODES', "Deleted $deletedCount orphaned episodes");
            
            return [
                'success' => true,
                'message' => "Se eliminaron $deletedCount episodios huérfanos",
                'deleted_count' => $deletedCount
            ];
        } else {
            $conn->rollBack();
            return [
                'success' => true,
                'message' => 'No se encontraron episodios huérfanos',
                'deleted_count' => 0
            ];
        }
        
    } catch (Exception $e) {
        $conn->rollBack();
        logAction('ERROR', 'cleanOrphanedEpisodes: ' . $e->getMessage());
        return ['success' => false, 'message' => 'Error al limpiar episodios: ' . $e->getMessage()];
    }
}
?>
