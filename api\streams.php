<?php
/**
 * API para gestión de streams
 * Endpoints para buscar, eliminar y administrar streams
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

// Configurar headers CORS y JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            handleGetRequest($action, $database);
            break;
            
        case 'POST':
            handlePostRequest($action, $database);
            break;
            
        case 'DELETE':
            handleDeleteRequest($action, $database);
            break;
            
        default:
            jsonResponse(null, false, 'Método no permitido');
    }
} catch (Exception $e) {
    logAction('API_ERROR', $e->getMessage());
    jsonResponse(null, false, 'Error interno del servidor');
}

/**
 * Manejar requests GET
 */
function handleGetRequest($action, $database) {
    switch ($action) {
        case 'search':
            searchStreamsAPI($database);
            break;
            
        case 'stats':
            getStatsAPI($database);
            break;
            
        case 'duplicates':
            getDuplicatesAPI($database);
            break;
            
        case 'incomplete_series':
            getIncompleteSeriesAPI($database);
            break;
            
        case 'preview_delete':
            previewDeleteAPI($database);
            break;
            
        default:
            jsonResponse(null, false, 'Acción no válida');
    }
}

/**
 * Manejar requests POST
 */
function handlePostRequest($action, $database) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($action) {
        case 'delete_streams':
            deleteStreamsAPI($database, $input);
            break;
            
        case 'clean_orphaned':
            cleanOrphanedAPI($database, $input);
            break;
            
        case 'delete_duplicates':
            deleteDuplicatesAPI($database, $input);
            break;
            
        default:
            jsonResponse(null, false, 'Acción no válida');
    }
}

/**
 * Manejar requests DELETE
 */
function handleDeleteRequest($action, $database) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($action) {
        case 'streams':
            deleteStreamsAPI($database, $input);
            break;
            
        default:
            jsonResponse(null, false, 'Acción no válida');
    }
}

/**
 * API para buscar streams
 */
function searchStreamsAPI($database) {
    $filters = [
        'source' => sanitizeInput($_GET['source'] ?? ''),
        'type' => sanitizeInput($_GET['type'] ?? ''),
        'display_name' => sanitizeInput($_GET['display_name'] ?? '')
    ];
    
    // Remover filtros vacíos
    $filters = array_filter($filters);
    
    $results = searchStreams($database, $filters);
    
    if ($results !== false) {
        jsonResponse($results, true, 'Búsqueda completada');
    } else {
        jsonResponse(null, false, 'Error en la búsqueda');
    }
}

/**
 * API para obtener estadísticas
 */
function getStatsAPI($database) {
    $stats = getDashboardStats($database);
    
    if ($stats !== false) {
        jsonResponse($stats, true, 'Estadísticas obtenidas');
    } else {
        jsonResponse(null, false, 'Error al obtener estadísticas');
    }
}

/**
 * API para obtener duplicados
 */
function getDuplicatesAPI($database) {
    $type = sanitizeInput($_GET['type'] ?? 'all');
    $results = getDuplicateStreams($database, $type);
    
    if ($results !== false) {
        // Agrupar por nombre para mejor visualización
        $grouped = [];
        foreach ($results as $stream) {
            $name = $stream['stream_display_name'];
            if (!isset($grouped[$name])) {
                $grouped[$name] = [];
            }
            $grouped[$name][] = $stream;
        }
        
        jsonResponse($grouped, true, 'Duplicados obtenidos');
    } else {
        jsonResponse(null, false, 'Error al obtener duplicados');
    }
}

/**
 * API para obtener series incompletas
 */
function getIncompleteSeriesAPI($database) {
    $results = getIncompleteSeriesData($database);
    
    if ($results !== false) {
        jsonResponse($results, true, 'Series incompletas obtenidas');
    } else {
        jsonResponse(null, false, 'Error al obtener series incompletas');
    }
}

/**
 * API para eliminar streams
 */
function deleteStreamsAPI($database, $input) {
    if (!isset($input['stream_ids']) || !is_array($input['stream_ids'])) {
        jsonResponse(null, false, 'IDs de streams requeridos');
        return;
    }
    
    $confirm = $input['confirm'] ?? false;
    $streamIds = array_map('intval', $input['stream_ids']);
    
    $result = deleteStreams($database, $streamIds, $confirm);
    
    if ($result['success']) {
        jsonResponse($result, true, $result['message']);
    } else {
        jsonResponse(null, false, $result['message']);
    }
}

/**
 * API para limpiar episodios huérfanos
 */
function cleanOrphanedAPI($database, $input) {
    $confirm = $input['confirm'] ?? false;
    
    $result = cleanOrphanedEpisodes($database, $confirm);
    
    if ($result['success']) {
        jsonResponse($result, true, $result['message']);
    } else {
        jsonResponse(null, false, $result['message']);
    }
}

/**
 * API para eliminar duplicados
 */
function deleteDuplicatesAPI($database, $input) {
    $confirm = $input['confirm'] ?? false;
    $type = $input['type'] ?? 'all';
    $strategy = $input['strategy'] ?? 'keep_oldest'; // keep_oldest, keep_symlink
    
    if (!$confirm) {
        jsonResponse(null, false, 'Confirmación requerida');
        return;
    }
    
    try {
        $conn = $database->getConnection();
        $conn->beginTransaction();
        
        $deletedCount = 0;
        
        if ($strategy === 'keep_symlink') {
            // Eliminar direct_source y direct_proxy si existe symlink
            $sql = "
                DELETE FROM streams
                WHERE id IN (
                    SELECT borrar.id
                    FROM (
                        SELECT s.id
                        FROM streams s
                        WHERE s.type = 2
                          AND (
                            (s.direct_source IS NOT NULL AND s.direct_source <> '')
                            OR (s.direct_proxy IS NOT NULL AND s.direct_proxy <> '')
                          )
                          AND s.stream_display_name IN (
                            SELECT stream_display_name
                            FROM streams
                            WHERE type = 2
                              AND movie_symlink IS NOT NULL
                              AND movie_symlink <> ''
                          )
                    ) AS borrar
                )
            ";
        } else {
            // Estrategia por defecto: mantener el más antiguo (menor ID)
            $sql = "
                DELETE FROM streams
                WHERE id IN (
                    SELECT borrar.id
                    FROM (
                        SELECT s.id
                        FROM streams s
                        JOIN (
                            SELECT stream_display_name, MIN(id) AS id_a_conservar
                            FROM streams
                            WHERE type = 2
                            GROUP BY stream_display_name
                            HAVING COUNT(*) > 1
                        ) t
                        ON s.stream_display_name = t.stream_display_name
                        WHERE s.type = 2
                          AND s.id <> t.id_a_conservar
                    ) AS borrar
                )
            ";
        }
        
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $deletedCount = $stmt->rowCount();
        
        $conn->commit();
        
        logAction('DELETE_DUPLICATES', "Strategy: $strategy, Deleted: $deletedCount");
        
        jsonResponse(
            ['deleted_count' => $deletedCount], 
            true, 
            "Se eliminaron $deletedCount streams duplicados"
        );
        
    } catch (Exception $e) {
        $conn->rollBack();
        logAction('ERROR', 'deleteDuplicatesAPI: ' . $e->getMessage());
        jsonResponse(null, false, 'Error al eliminar duplicados: ' . $e->getMessage());
    }
}

/**
 * API para previsualizar eliminación
 */
function previewDeleteAPI($database) {
    $source = sanitizeInput($_GET['source'] ?? '');
    
    if (empty($source)) {
        jsonResponse(null, false, 'Source requerido para preview');
        return;
    }
    
    try {
        $conn = $database->getConnection();
        
        $sql = "SELECT COUNT(*) as count FROM streams WHERE stream_source LIKE :source";
        $stmt = $conn->prepare($sql);
        $stmt->execute([':source' => "%$source%"]);
        $count = $stmt->fetch()['count'];
        
        jsonResponse(['count' => $count], true, "Se encontraron $count streams para eliminar");
        
    } catch (Exception $e) {
        logAction('ERROR', 'previewDeleteAPI: ' . $e->getMessage());
        jsonResponse(null, false, 'Error en preview');
    }
}
?>
