# XUI Admin Panel 🎮

Panel de administración con estilo Xbox para gestión de streams XUI (Xtream UI). Una interfaz moderna y expresiva para administrar tu base de datos de streams, limpiar duplicados y gestionar series.

## 🚀 Características

- **Interfaz estilo Xbox**: Diseño moderno inspirado en Xbox con colores verdes y botones expresivos
- **Gestión de Streams**: Buscar, filtrar y eliminar streams por source, tipo y nombre
- **Limpieza de Duplicados**: Detectar y eliminar streams duplicados con diferentes estrategias
- **Series Incompletas**: Identificar series con episodios faltantes
- **Herramientas de Mantenimiento**: Limpiar episodios huérfanos y otras tareas de mantenimiento
- **Sistema de Confirmación**: Confirmaciones antes de operaciones destructivas
- **Logging**: Registro de todas las acciones administrativas
- **Responsive**: Funciona en desktop y móvil

## 📋 Requisitos

- PHP 7.4 o superior
- MySQL/MariaDB
- Base de datos XUI (Xtream UI)
- Servidor web (Apache/Nginx)

## 🛠️ Instalación

1. **Clonar o descargar** los archivos en tu servidor web

2. **Configurar la base de datos**:
   ```bash
   cp config.example.php config.php
   ```
   
   Edita `config.php` con tus datos de conexión:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'xtream_iptvpro');  // Tu base de datos XUI
   define('DB_USER', 'tu_usuario');
   define('DB_PASS', 'tu_contraseña');
   ```

3. **Configurar permisos**:
   ```bash
   chmod 755 logs/
   chmod 755 cache/
   chmod 755 exports/
   ```

4. **Acceder al panel**:
   Abre tu navegador y ve a: `http://tu-servidor/ruta-al-panel/`

## 🎯 Uso

### Dashboard
- Vista general con estadísticas de la base de datos
- Contadores de streams totales, duplicados y series incompletas
- Acciones rápidas para funciones principales

### Gestión de Streams
- **Buscar por Source**: Encuentra streams por source específico (ej: `gotv.vip`)
- **Filtrar por Tipo**: Live TV, Movies, Series
- **Buscar por Nombre**: Filtrar por nombre del stream
- **Eliminación Masiva**: Seleccionar múltiples streams para eliminar

### Limpieza de Duplicados
- **Detectar Duplicados**: Encuentra streams con el mismo nombre
- **Estrategias de Limpieza**:
  - Mantener el más antiguo (menor ID)
  - Mantener symlinks (eliminar direct_source/direct_proxy si existe symlink)
- **Vista Agrupada**: Visualiza duplicados agrupados por nombre

### Series Incompletas
- **Detectar Episodios Faltantes**: Encuentra series con episodios faltantes en secuencia
- **Estadísticas Detalladas**: Muestra episodios disponibles vs esperados
- **Porcentaje de Completitud**: Indica qué tan completa está cada temporada

### Herramientas
- **Limpiar Episodios Huérfanos**: Elimina episodios sin stream asociado
- **Búsqueda Rápida por Source**: Herramienta rápida para buscar por source
- **Estadísticas de Base de Datos**: Vista detallada de estadísticas
- **Exportar Logs**: Descargar logs de acciones (en desarrollo)

## 🔧 Configuración Avanzada

### Personalización de Mensajes
Edita `config.php` para personalizar mensajes:
```php
$messages = [
    'welcome' => 'Tu mensaje personalizado',
    'no_results' => 'Mensaje cuando no hay resultados',
    // ...
];
```

### Configuración de Seguridad
```php
define('ADMIN_PASSWORD', 'tu_contraseña_segura');
define('SESSION_TIMEOUT', 3600);
$security_config = [
    'enable_ip_whitelist' => true,
    'allowed_ips' => ['*************', '*********']
];
```

### Configuración de Rendimiento
```php
$performance_config = [
    'max_execution_time' => 300,
    'memory_limit' => '512M',
    'enable_cache' => true
];
```

## 📊 Consultas SQL Incluidas

El panel implementa las consultas SQL del archivo `consultassql.txt`:

- ✅ Buscar streams por source (`gotv.vip`, etc.)
- ✅ Eliminar streams por criterios
- ✅ Detectar y eliminar duplicados
- ✅ Limpiar episodios huérfanos
- ✅ Encontrar series incompletas
- ✅ Estrategias de limpieza conservadoras

## 🎨 Personalización Visual

### Cambiar Colores
Edita `assets/css/xbox-style.css`:
```css
:root {
    --xbox-green: #107C10;        /* Color principal */
    --xbox-green-light: #16A016;  /* Color hover */
    --xbox-accent: #00BCF2;       /* Color de acento */
}
```

### Agregar Nuevos Temas
1. Crea un nuevo archivo CSS en `assets/css/`
2. Modifica la configuración en `config.php`:
   ```php
   $ui_config = [
       'theme' => 'tu_tema_personalizado'
   ];
   ```

## 🔒 Seguridad

- **Validación de Entrada**: Todos los inputs son sanitizados
- **Confirmaciones**: Operaciones destructivas requieren confirmación
- **Logging**: Todas las acciones se registran con timestamp
- **Transacciones**: Operaciones de base de datos usan transacciones
- **Escape de HTML**: Prevención de XSS en la salida

## 📝 Logging

Todas las acciones se registran en `logs/admin_actions.log`:
```
[2024-01-15 10:30:45] [admin] SEARCH_STREAMS - Filters: {"source":"gotv.vip"}, Results: 150
[2024-01-15 10:31:20] [admin] DELETE_STREAMS - Deleted 25 streams. IDs: 1001,1002,1003...
```

## 🐛 Solución de Problemas

### Error de Conexión a Base de Datos
1. Verifica las credenciales en `config.php`
2. Asegúrate de que el servidor MySQL esté ejecutándose
3. Verifica que el usuario tenga permisos en la base de datos

### Permisos de Archivos
```bash
# Dar permisos de escritura a directorios necesarios
chmod 755 logs/ cache/ exports/
chown www-data:www-data logs/ cache/ exports/
```

### Memoria Insuficiente
Aumenta el límite de memoria en `config.php`:
```php
$performance_config = [
    'memory_limit' => '512M'  // o más según necesites
];
```

## 🚧 Desarrollo Futuro

- [ ] Sistema de usuarios y roles
- [ ] Backup automático antes de operaciones
- [ ] Exportación de reportes en CSV/PDF
- [ ] API REST completa
- [ ] Notificaciones por email
- [ ] Programación de tareas automáticas
- [ ] Monitoreo en tiempo real
- [ ] Integración con webhooks

## 📄 Licencia

Este proyecto es de código abierto. Puedes usarlo, modificarlo y distribuirlo libremente.

## 🤝 Contribuciones

Las contribuciones son bienvenidas. Por favor:

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit tus cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Abre un Pull Request

## 📞 Soporte

Si encuentras algún problema o tienes sugerencias:

1. Revisa la sección de solución de problemas
2. Verifica los logs en `logs/admin_actions.log`
3. Abre un issue en el repositorio

---

**¡Disfruta administrando tu XUI con estilo Xbox! 🎮**
