/**
 * Dashboard JavaScript - Funcionalidad específica del dashboard
 */

/**
 * Cargar datos del dashboard
 */
async function loadDashboardData() {
    try {
        const response = await fetch('api/streams.php?action=stats');
        const data = await response.json();
        
        if (data.success) {
            updateDashboardStats(data.data);
        } else {
            console.error('Error al cargar estadísticas:', data.message);
        }
    } catch (error) {
        console.error('Error al cargar dashboard:', error);
    }
}

/**
 * Actualizar estadísticas en el dashboard
 */
function updateDashboardStats(stats) {
    // Total de streams
    const totalElement = document.getElementById('total-streams');
    if (totalElement) {
        totalElement.textContent = formatNumber(stats.total_streams || 0);
    }
    
    // Streams duplicados
    const duplicatesElement = document.getElementById('duplicate-streams');
    if (duplicatesElement) {
        duplicatesElement.textContent = formatNumber(stats.duplicate_streams || 0);
        duplicatesElement.className = stats.duplicate_streams > 0 ? 'text-warning' : 'text-success';
    }
    
    // Series incompletas
    const seriesElement = document.getElementById('incomplete-series');
    if (seriesElement) {
        seriesElement.textContent = formatNumber(stats.incomplete_series || 0);
        seriesElement.className = stats.incomplete_series > 0 ? 'text-warning' : 'text-success';
    }
}

/**
 * Crear sección dinámica para duplicados
 */
function loadDuplicatesSection() {
    const mainContent = document.querySelector('.xbox-main');
    
    // Verificar si la sección ya existe
    let duplicatesSection = document.getElementById('duplicates');
    if (!duplicatesSection) {
        duplicatesSection = document.createElement('section');
        duplicatesSection.id = 'duplicates';
        duplicatesSection.className = 'content-section';
        mainContent.appendChild(duplicatesSection);
    }
    
    duplicatesSection.innerHTML = `
        <div class="section-header">
            <h2><i class="fas fa-copy"></i> Gestión de Duplicados</h2>
            <p>Detectar y eliminar streams duplicados en la base de datos</p>
        </div>

        <div class="search-panel">
            <div class="search-form">
                <div class="form-group">
                    <label for="duplicate-type">Tipo de Duplicados:</label>
                    <select id="duplicate-type" class="xbox-select">
                        <option value="all">Todos los duplicados</option>
                        <option value="with_direct_source">Con direct_source</option>
                        <option value="with_symlink">Con symlink</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="cleanup-strategy">Estrategia de Limpieza:</label>
                    <select id="cleanup-strategy" class="xbox-select">
                        <option value="keep_oldest">Mantener el más antiguo</option>
                        <option value="keep_symlink">Mantener symlinks</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button class="xbox-btn primary" onclick="searchDuplicates()">
                        <i class="fas fa-search"></i> Buscar Duplicados
                    </button>
                    <button class="xbox-btn warning" onclick="cleanupDuplicates()">
                        <i class="fas fa-broom"></i> Limpiar Duplicados
                    </button>
                </div>
            </div>
        </div>

        <div id="duplicates-results" class="results-panel">
            <div class="empty-state">
                <i class="fas fa-copy"></i>
                <h3>Buscar Duplicados</h3>
                <p>Selecciona el tipo de duplicados y haz clic en "Buscar Duplicados"</p>
            </div>
        </div>
    `;
    
    duplicatesSection.classList.add('active');
}

/**
 * Crear sección dinámica para series
 */
function loadSeriesSection() {
    const mainContent = document.querySelector('.xbox-main');
    
    let seriesSection = document.getElementById('series');
    if (!seriesSection) {
        seriesSection = document.createElement('section');
        seriesSection.id = 'series';
        seriesSection.className = 'content-section';
        mainContent.appendChild(seriesSection);
    }
    
    seriesSection.innerHTML = `
        <div class="section-header">
            <h2><i class="fas fa-tv"></i> Series Incompletas</h2>
            <p>Detectar series con episodios faltantes</p>
        </div>

        <div class="search-panel">
            <div class="search-form">
                <div class="form-actions">
                    <button class="xbox-btn primary" onclick="loadIncompleteSeries()">
                        <i class="fas fa-search"></i> Buscar Series Incompletas
                    </button>
                    <button class="xbox-btn secondary" onclick="exportSeriesReport()">
                        <i class="fas fa-download"></i> Exportar Reporte
                    </button>
                </div>
            </div>
        </div>

        <div id="series-results" class="results-panel">
            <div class="empty-state">
                <i class="fas fa-tv"></i>
                <h3>Series Incompletas</h3>
                <p>Haz clic en "Buscar Series Incompletas" para ver las series con episodios faltantes</p>
            </div>
        </div>
    `;
    
    seriesSection.classList.add('active');
}

/**
 * Crear sección dinámica para herramientas
 */
function loadToolsSection() {
    const mainContent = document.querySelector('.xbox-main');
    
    let toolsSection = document.getElementById('tools');
    if (!toolsSection) {
        toolsSection = document.createElement('section');
        toolsSection.id = 'tools';
        toolsSection.className = 'content-section';
        mainContent.appendChild(toolsSection);
    }
    
    toolsSection.innerHTML = `
        <div class="section-header">
            <h2><i class="fas fa-tools"></i> Herramientas de Mantenimiento</h2>
            <p>Herramientas para limpieza y mantenimiento de la base de datos</p>
        </div>

        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-trash-alt"></i>
                </div>
                <div class="card-content">
                    <h3>Limpiar Episodios Huérfanos</h3>
                    <p>Eliminar episodios que no tienen stream asociado</p>
                    <button class="xbox-btn warning" onclick="cleanOrphanedEpisodes()">
                        <i class="fas fa-broom"></i> Limpiar
                    </button>
                </div>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-search"></i>
                </div>
                <div class="card-content">
                    <h3>Buscar por Source</h3>
                    <p>Buscar streams por source específico (ej: gotv.vip)</p>
                    <div style="margin-top: 1rem;">
                        <input type="text" id="tool-source-search" placeholder="Ej: gotv.vip" class="xbox-input" style="margin-bottom: 0.5rem;">
                        <button class="xbox-btn primary" onclick="searchBySource()">
                            <i class="fas fa-search"></i> Buscar
                        </button>
                    </div>
                </div>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-database"></i>
                </div>
                <div class="card-content">
                    <h3>Estadísticas de Base de Datos</h3>
                    <p>Ver estadísticas detalladas de la base de datos</p>
                    <button class="xbox-btn accent" onclick="showDatabaseStats()">
                        <i class="fas fa-chart-bar"></i> Ver Stats
                    </button>
                </div>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-file-export"></i>
                </div>
                <div class="card-content">
                    <h3>Exportar Logs</h3>
                    <p>Descargar logs de acciones del administrador</p>
                    <button class="xbox-btn secondary" onclick="exportLogs()">
                        <i class="fas fa-download"></i> Exportar
                    </button>
                </div>
            </div>
        </div>
    `;
    
    toolsSection.classList.add('active');
}

/**
 * Crear sección dinámica
 */
function createDynamicSection(sectionName) {
    switch (sectionName) {
        case 'duplicates':
            loadDuplicatesSection();
            break;
        case 'series':
            loadSeriesSection();
            break;
        case 'tools':
            loadToolsSection();
            break;
        default:
            console.warn('Sección no reconocida:', sectionName);
    }
}

/**
 * Buscar duplicados
 */
async function searchDuplicates() {
    const type = document.getElementById('duplicate-type')?.value || 'all';
    const resultsPanel = document.getElementById('duplicates-results');
    
    if (!resultsPanel) return;
    
    resultsPanel.innerHTML = `
        <div class="loading">
            <i class="fas fa-spinner"></i>
            Buscando duplicados...
        </div>
    `;
    
    try {
        const response = await fetch(`api/streams.php?action=duplicates&type=${type}`);
        const data = await response.json();
        
        if (data.success) {
            displayDuplicatesResults(data.data);
        } else {
            throw new Error(data.message);
        }
    } catch (error) {
        console.error('Error al buscar duplicados:', error);
        resultsPanel.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>Error en la búsqueda</h3>
                <p>${error.message}</p>
            </div>
        `;
    }
}

/**
 * Mostrar resultados de duplicados
 */
function displayDuplicatesResults(duplicates) {
    const resultsPanel = document.getElementById('duplicates-results');
    const groupNames = Object.keys(duplicates);
    
    if (groupNames.length === 0) {
        resultsPanel.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-check-circle"></i>
                <h3>No se encontraron duplicados</h3>
                <p>¡Excelente! No hay streams duplicados en la base de datos</p>
            </div>
        `;
        return;
    }
    
    let html = `
        <div class="results-header">
            <h3><i class="fas fa-copy"></i> Streams Duplicados</h3>
            <span class="results-count">${groupNames.length} grupos</span>
        </div>
        <div class="results-content">
    `;
    
    groupNames.forEach(groupName => {
        const streams = duplicates[groupName];
        html += `
            <div class="duplicate-group" style="margin-bottom: 2rem; border: 2px solid var(--xbox-light-gray); border-radius: 8px; padding: 1rem;">
                <h4 style="color: var(--xbox-white); margin-bottom: 1rem;">
                    <i class="fas fa-layer-group"></i> ${escapeHtml(groupName)} 
                    <span style="color: var(--xbox-warning);">(${streams.length} duplicados)</span>
                </h4>
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Source</th>
                            <th>Direct Source</th>
                            <th>Symlink</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        streams.forEach(stream => {
            html += `
                <tr>
                    <td>${stream.id}</td>
                    <td>${escapeHtml(truncateText(stream.stream_source || 'N/A', 30))}</td>
                    <td>${stream.direct_source ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-error"></i>'}</td>
                    <td>${stream.movie_symlink ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-error"></i>'}</td>
                    <td>
                        <button class="xbox-btn danger btn-small" onclick="deleteStream(${stream.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        });
        
        html += `
                    </tbody>
                </table>
            </div>
        `;
    });
    
    html += '</div>';
    resultsPanel.innerHTML = html;
}

/**
 * Cargar series incompletas
 */
async function loadIncompleteSeries() {
    const resultsPanel = document.getElementById('series-results');

    if (!resultsPanel) return;

    resultsPanel.innerHTML = `
        <div class="loading">
            <i class="fas fa-spinner"></i>
            Buscando series incompletas...
        </div>
    `;

    try {
        const response = await fetch('api/streams.php?action=incomplete_series');
        const data = await response.json();

        if (data.success) {
            displayIncompleteSeriesResults(data.data);
        } else {
            throw new Error(data.message);
        }
    } catch (error) {
        console.error('Error al cargar series incompletas:', error);
        resultsPanel.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>Error al cargar series</h3>
                <p>${error.message}</p>
            </div>
        `;
    }
}

/**
 * Mostrar resultados de series incompletas
 */
function displayIncompleteSeriesResults(series) {
    const resultsPanel = document.getElementById('series-results');

    if (series.length === 0) {
        resultsPanel.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-check-circle"></i>
                <h3>No se encontraron series incompletas</h3>
                <p>¡Excelente! Todas las series tienen episodios completos</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="results-header">
            <h3><i class="fas fa-tv"></i> Series Incompletas</h3>
            <span class="results-count">${series.length} series</span>
        </div>
        <div class="results-content">
            <table class="results-table">
                <thead>
                    <tr>
                        <th>Serie</th>
                        <th>Temporada</th>
                        <th>Primer Ep.</th>
                        <th>Último Ep.</th>
                        <th>Disponibles</th>
                        <th>Esperados</th>
                        <th>Faltantes</th>
                        <th>% Completo</th>
                    </tr>
                </thead>
                <tbody>
    `;

    series.forEach(serie => {
        const percentage = Math.round((serie.episodios_disponibles / serie.episodios_esperados) * 100);
        const statusClass = percentage >= 80 ? 'text-success' : percentage >= 50 ? 'text-warning' : 'text-error';

        html += `
            <tr>
                <td><strong>${escapeHtml(serie.title)}</strong></td>
                <td>T${serie.season_num}</td>
                <td>${serie.primer_ep}</td>
                <td>${serie.ultimo_ep}</td>
                <td>${serie.episodios_disponibles}</td>
                <td>${serie.episodios_esperados}</td>
                <td class="text-warning"><strong>${serie.faltantes}</strong></td>
                <td class="${statusClass}"><strong>${percentage}%</strong></td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    resultsPanel.innerHTML = html;
}

/**
 * Limpiar episodios huérfanos
 */
async function cleanOrphanedEpisodes() {
    showConfirmationDialog(
        'Limpiar Episodios Huérfanos',
        '¿Estás seguro de que quieres eliminar los episodios huérfanos?',
        'Los episodios huérfanos son aquellos que no tienen un stream asociado en la tabla streams.',
        async () => {
            try {
                const response = await fetch('api/streams.php?action=clean_orphaned', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ confirm: true })
                });

                const data = await response.json();

                if (data.success) {
                    showNotification(data.message, 'success');
                    loadDashboardData(); // Actualizar estadísticas
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('Error al limpiar episodios huérfanos:', error);
                showNotification('Error al limpiar episodios: ' + error.message, 'error');
            }
        }
    );
}

/**
 * Buscar por source desde herramientas
 */
function searchBySource() {
    const source = document.getElementById('tool-source-search')?.value;

    if (!source) {
        showNotification('Por favor ingresa un source para buscar', 'warning');
        return;
    }

    // Cambiar a la sección de streams y realizar búsqueda
    showSection('streams');

    // Esperar a que la sección se cargue y luego llenar el campo
    setTimeout(() => {
        const searchSourceField = document.getElementById('search-source');
        if (searchSourceField) {
            searchSourceField.value = source;
            searchStreams();
        }
    }, 100);
}

/**
 * Limpiar duplicados
 */
async function cleanupDuplicates() {
    const strategy = document.getElementById('cleanup-strategy')?.value || 'keep_oldest';

    showConfirmationDialog(
        'Limpiar Streams Duplicados',
        '¿Estás seguro de que quieres eliminar los streams duplicados?',
        `Se utilizará la estrategia: ${strategy === 'keep_oldest' ? 'Mantener el más antiguo' : 'Mantener symlinks'}. Esta acción no se puede deshacer.`,
        async () => {
            try {
                const response = await fetch('api/streams.php?action=delete_duplicates', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        confirm: true,
                        strategy: strategy
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showNotification(data.message, 'success');
                    searchDuplicates(); // Actualizar resultados
                    loadDashboardData(); // Actualizar estadísticas
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error('Error al limpiar duplicados:', error);
                showNotification('Error al limpiar duplicados: ' + error.message, 'error');
            }
        }
    );
}

/**
 * Mostrar estadísticas de base de datos
 */
async function showDatabaseStats() {
    try {
        const response = await fetch('api/streams.php?action=stats');
        const data = await response.json();

        if (data.success) {
            const stats = data.data;

            let statsHtml = `
                <h4>Estadísticas Generales</h4>
                <ul style="list-style: none; padding: 0;">
                    <li style="margin-bottom: 0.5rem;"><strong>Total de Streams:</strong> ${formatNumber(stats.total_streams)}</li>
                    <li style="margin-bottom: 0.5rem;"><strong>Streams Duplicados:</strong> ${formatNumber(stats.duplicate_streams)}</li>
                    <li style="margin-bottom: 0.5rem;"><strong>Series Incompletas:</strong> ${formatNumber(stats.incomplete_series)}</li>
                </ul>
            `;

            if (stats.streams_by_type && stats.streams_by_type.length > 0) {
                statsHtml += `
                    <h4 style="margin-top: 2rem;">Streams por Tipo</h4>
                    <ul style="list-style: none; padding: 0;">
                `;

                stats.streams_by_type.forEach(type => {
                    statsHtml += `
                        <li style="margin-bottom: 0.5rem;">
                            <strong>${type.type_name || 'Tipo ' + type.type_id}:</strong> ${formatNumber(type.count)}
                        </li>
                    `;
                });

                statsHtml += '</ul>';
            }

            showInfoModal('Estadísticas de Base de Datos', statsHtml);
        } else {
            throw new Error(data.message);
        }
    } catch (error) {
        console.error('Error al cargar estadísticas:', error);
        showNotification('Error al cargar estadísticas: ' + error.message, 'error');
    }
}

/**
 * Mostrar modal de información
 */
function showInfoModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="modal-close" onclick="closeModal(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
            <div class="modal-footer">
                <button class="xbox-btn primary" onclick="closeModal(this)">
                    <i class="fas fa-check"></i> Cerrar
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    setTimeout(() => modal.classList.add('active'), 10);
}

/**
 * Exportar reporte de series
 */
function exportSeriesReport() {
    showNotification('Función de exportación en desarrollo', 'info');
}

/**
 * Exportar logs
 */
function exportLogs() {
    showNotification('Función de exportación de logs en desarrollo', 'info');
}

/**
 * Ver detalles de stream
 */
function viewStreamDetails(streamId) {
    showNotification(`Ver detalles del stream ${streamId} - Función en desarrollo`, 'info');
}

/**
 * Formatear números
 */
function formatNumber(num) {
    return new Intl.NumberFormat('es-ES').format(num);
}
