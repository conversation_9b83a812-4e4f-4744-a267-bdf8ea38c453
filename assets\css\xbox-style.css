/**
 * Xbox Style CSS - Estilo inspirado en Xbox para XUI Admin Panel
 */

:root {
    /* Colores Xbox */
    --xbox-green: #107C10;
    --xbox-green-light: #16A016;
    --xbox-green-dark: #0E6B0E;
    --xbox-black: #0E1E0E;
    --xbox-dark-gray: #1E1E1E;
    --xbox-gray: #2D2D30;
    --xbox-light-gray: #3E3E42;
    --xbox-white: #FFFFFF;
    --xbox-accent: #00BCF2;
    --xbox-warning: #FFB900;
    --xbox-error: #D13438;
    
    /* Gradientes */
    --xbox-gradient: linear-gradient(135deg, var(--xbox-green) 0%, var(--xbox-green-light) 100%);
    --xbox-gradient-dark: linear-gradient(135deg, var(--xbox-black) 0%, var(--xbox-dark-gray) 100%);
    
    /* Sombras */
    --xbox-shadow: 0 4px 20px rgba(16, 124, 16, 0.3);
    --xbox-shadow-hover: 0 6px 25px rgba(16, 124, 16, 0.4);
    --xbox-shadow-dark: 0 2px 10px rgba(0, 0, 0, 0.5);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--xbox-gradient-dark);
    color: var(--xbox-white);
    min-height: 100vh;
    overflow-x: hidden;
}

/* Container Principal */
.xbox-container {
    display: grid;
    grid-template-areas: 
        "header header"
        "nav main";
    grid-template-rows: 80px 1fr;
    grid-template-columns: 250px 1fr;
    min-height: 100vh;
}

/* Header */
.xbox-header {
    grid-area: header;
    background: var(--xbox-gradient);
    padding: 0 2rem;
    display: flex;
    align-items: center;
    box-shadow: var(--xbox-shadow);
    position: relative;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo i {
    font-size: 2rem;
    color: var(--xbox-white);
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--xbox-white);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-online {
    color: var(--xbox-white);
    font-weight: 500;
}

.status-online i {
    color: #4CAF50;
    animation: pulse 2s infinite;
}

.status-offline {
    color: var(--xbox-error);
    font-weight: 500;
}

.status-offline i {
    color: var(--xbox-error);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Navigation */
.xbox-nav {
    grid-area: nav;
    background: var(--xbox-dark-gray);
    padding: 1rem 0;
    border-right: 2px solid var(--xbox-green);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
    position: relative;
}

.nav-item:hover {
    background: var(--xbox-gray);
    border-left-color: var(--xbox-green-light);
}

.nav-item.active {
    background: var(--xbox-green);
    border-left-color: var(--xbox-white);
    box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.1);
}

.nav-item i {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.nav-item span {
    font-weight: 500;
    font-size: 0.95rem;
}

/* Main Content */
.xbox-main {
    grid-area: main;
    background: var(--xbox-gray);
    padding: 2rem;
    overflow-y: auto;
}

/* Content Sections */
.content-section {
    display: none;
    animation: fadeIn 0.3s ease;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.section-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--xbox-green);
}

.section-header h2 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--xbox-white);
}

.section-header p {
    color: var(--xbox-light-gray);
    font-size: 1.1rem;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background: var(--xbox-dark-gray);
    border-radius: 12px;
    padding: 1.5rem;
    border: 2px solid var(--xbox-light-gray);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--xbox-gradient);
}

.dashboard-card:hover {
    border-color: var(--xbox-green);
    transform: translateY(-5px);
    box-shadow: var(--xbox-shadow);
}

.dashboard-card .card-icon {
    font-size: 2.5rem;
    color: var(--xbox-green);
    margin-bottom: 1rem;
}

.dashboard-card h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: var(--xbox-white);
}

.dashboard-card p {
    color: var(--xbox-light-gray);
    font-size: 1rem;
}

/* Botones Xbox */
.xbox-btn {
    background: var(--xbox-gradient);
    color: var(--xbox-white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    box-shadow: var(--xbox-shadow-dark);
}

.xbox-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--xbox-shadow-hover);
    background: var(--xbox-green-light);
}

.xbox-btn:active {
    transform: translateY(0);
}

.xbox-btn.secondary {
    background: var(--xbox-gray);
    border: 2px solid var(--xbox-green);
}

.xbox-btn.secondary:hover {
    background: var(--xbox-green);
}

.xbox-btn.accent {
    background: linear-gradient(135deg, var(--xbox-accent) 0%, #0099CC 100%);
}

.xbox-btn.warning {
    background: linear-gradient(135deg, var(--xbox-warning) 0%, #E6A500 100%);
}

.xbox-btn.danger {
    background: linear-gradient(135deg, var(--xbox-error) 0%, #B02A2E 100%);
}

/* Quick Actions */
.quick-actions {
    background: var(--xbox-dark-gray);
    border-radius: 12px;
    padding: 2rem;
    border: 2px solid var(--xbox-light-gray);
}

.quick-actions h3 {
    margin-bottom: 1.5rem;
    color: var(--xbox-white);
    font-size: 1.3rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Text Colors */
.text-success {
    color: #4CAF50;
}

.text-error {
    color: var(--xbox-error);
}

.text-warning {
    color: var(--xbox-warning);
}

/* Responsive */
@media (max-width: 768px) {
    .xbox-container {
        grid-template-areas: 
            "header"
            "nav"
            "main";
        grid-template-rows: 80px auto 1fr;
        grid-template-columns: 1fr;
    }
    
    .xbox-nav {
        display: flex;
        overflow-x: auto;
        padding: 0.5rem;
    }
    
    .nav-item {
        min-width: 120px;
        flex-direction: column;
        text-align: center;
        padding: 0.75rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
