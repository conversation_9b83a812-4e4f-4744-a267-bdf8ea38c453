/**
 * Main CSS - Estilos adicionales para componentes específicos
 */

/* Search Panel */
.search-panel {
    background: var(--xbox-dark-gray);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 2px solid var(--xbox-light-gray);
}

.search-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    align-items: end;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    color: var(--xbox-white);
    font-weight: 500;
    font-size: 0.95rem;
}

.xbox-input, .xbox-select {
    background: var(--xbox-gray);
    border: 2px solid var(--xbox-light-gray);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    color: var(--xbox-white);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.xbox-input:focus, .xbox-select:focus {
    outline: none;
    border-color: var(--xbox-green);
    box-shadow: 0 0 0 3px rgba(16, 124, 16, 0.2);
}

.xbox-input::placeholder {
    color: var(--xbox-light-gray);
}

.xbox-select option {
    background: var(--xbox-gray);
    color: var(--xbox-white);
}

.form-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Results Panel */
.results-panel {
    background: var(--xbox-dark-gray);
    border-radius: 12px;
    border: 2px solid var(--xbox-light-gray);
    min-height: 400px;
    position: relative;
}

.results-header {
    background: var(--xbox-gradient);
    padding: 1rem 1.5rem;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.results-header h3 {
    color: var(--xbox-white);
    font-size: 1.2rem;
}

.results-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.9rem;
    color: var(--xbox-white);
}

.results-content {
    padding: 1.5rem;
}

.results-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.results-table th,
.results-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--xbox-light-gray);
}

.results-table th {
    background: var(--xbox-gray);
    color: var(--xbox-white);
    font-weight: 600;
    position: sticky;
    top: 0;
}

.results-table td {
    color: var(--xbox-light-gray);
}

.results-table tr:hover {
    background: var(--xbox-gray);
}

/* Action Buttons in Table */
.table-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-small {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
    border-radius: 6px;
}

/* Loading States */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: var(--xbox-light-gray);
}

.loading i {
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--xbox-light-gray);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--xbox-green);
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: var(--xbox-white);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--xbox-dark-gray);
    border-radius: 12px;
    border: 2px solid var(--xbox-green);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    background: var(--xbox-gradient);
    padding: 1.5rem;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: var(--xbox-white);
    font-size: 1.3rem;
}

.modal-close {
    background: none;
    border: none;
    color: var(--xbox-white);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--xbox-light-gray);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Confirmation Dialog */
.confirmation-dialog {
    text-align: center;
}

.confirmation-dialog .warning-icon {
    font-size: 4rem;
    color: var(--xbox-warning);
    margin-bottom: 1rem;
}

.confirmation-dialog h3 {
    color: var(--xbox-white);
    margin-bottom: 1rem;
}

.confirmation-dialog p {
    color: var(--xbox-light-gray);
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Progress Bar */
.progress-bar {
    background: var(--xbox-gray);
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-fill {
    background: var(--xbox-gradient);
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
    width: 0%;
}

/* Notifications */
.notification {
    position: fixed;
    top: 100px;
    right: 2rem;
    background: var(--xbox-dark-gray);
    border: 2px solid var(--xbox-green);
    border-radius: 8px;
    padding: 1rem 1.5rem;
    color: var(--xbox-white);
    box-shadow: var(--xbox-shadow);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1001;
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-color: #4CAF50;
}

.notification.error {
    border-color: var(--xbox-error);
}

.notification.warning {
    border-color: var(--xbox-warning);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--xbox-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--xbox-green);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--xbox-green-light);
}
