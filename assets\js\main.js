/**
 * XUI Admin Panel - JavaScript Principal
 * Funcionalidad principal para navegación y gestión de la interfaz
 */

// Variables globales
let currentSection = 'dashboard';
let selectedStreams = [];

// Inicialización cuando el DOM está listo
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * Inicializar la aplicación
 */
function initializeApp() {
    setupNavigation();
    loadDashboardData();
    setupEventListeners();
    
    // Mostrar notificación de bienvenida
    showNotification('Panel de administración XUI cargado correctamente', 'success');
}

/**
 * Configurar navegación
 */
function setupNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            const section = this.dataset.section;
            showSection(section);
        });
    });
}

/**
 * Mostrar sección específica
 */
function showSection(sectionName) {
    // Actualizar navegación
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');
    
    // Mostrar contenido
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    let targetSection = document.getElementById(sectionName);
    if (targetSection) {
        targetSection.classList.add('active');
        currentSection = sectionName;
        
        // Cargar datos específicos de la sección
        loadSectionData(sectionName);
    } else {
        // Crear sección dinámicamente si no existe
        createDynamicSection(sectionName);
    }
}

/**
 * Cargar datos específicos de cada sección
 */
function loadSectionData(sectionName) {
    switch (sectionName) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'streams':
            // Los datos se cargan cuando el usuario busca
            break;
        case 'duplicates':
            loadDuplicatesSection();
            break;
        case 'series':
            loadSeriesSection();
            break;
        case 'tools':
            loadToolsSection();
            break;
    }
}

/**
 * Configurar event listeners
 */
function setupEventListeners() {
    // Búsqueda de streams
    const searchBtn = document.querySelector('[onclick="searchStreams()"]');
    if (searchBtn) {
        searchBtn.onclick = searchStreams;
    }
    
    // Limpiar búsqueda
    const clearBtn = document.querySelector('[onclick="clearSearch()"]');
    if (clearBtn) {
        clearBtn.onclick = clearSearch;
    }
    
    // Enter en campos de búsqueda
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && e.target.classList.contains('xbox-input')) {
            if (currentSection === 'streams') {
                searchStreams();
            }
        }
    });
}

/**
 * Realizar búsqueda de streams
 */
async function searchStreams() {
    const source = document.getElementById('search-source')?.value || '';
    const type = document.getElementById('stream-type')?.value || '';
    const displayName = document.getElementById('search-display-name')?.value || '';
    
    const resultsPanel = document.getElementById('streams-results');
    if (!resultsPanel) return;
    
    // Mostrar loading
    resultsPanel.innerHTML = `
        <div class="loading">
            <i class="fas fa-spinner"></i>
            Buscando streams...
        </div>
    `;
    
    try {
        const params = new URLSearchParams();
        if (source) params.append('source', source);
        if (type) params.append('type', type);
        if (displayName) params.append('display_name', displayName);
        
        const response = await fetch(`api/streams.php?action=search&${params}`);
        const data = await response.json();
        
        if (data.success) {
            displayStreamsResults(data.data);
            showNotification(`Se encontraron ${data.data.length} streams`, 'success');
        } else {
            throw new Error(data.message);
        }
        
    } catch (error) {
        console.error('Error en búsqueda:', error);
        resultsPanel.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>Error en la búsqueda</h3>
                <p>${error.message}</p>
            </div>
        `;
        showNotification('Error al buscar streams', 'error');
    }
}

/**
 * Mostrar resultados de streams
 */
function displayStreamsResults(streams) {
    const resultsPanel = document.getElementById('streams-results');
    
    if (streams.length === 0) {
        resultsPanel.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-search"></i>
                <h3>No se encontraron resultados</h3>
                <p>Intenta con otros criterios de búsqueda</p>
            </div>
        `;
        return;
    }
    
    let html = `
        <div class="results-header">
            <h3><i class="fas fa-list"></i> Resultados de Búsqueda</h3>
            <span class="results-count">${streams.length} streams</span>
        </div>
        <div class="results-content">
            <div class="table-actions-top">
                <button class="xbox-btn secondary btn-small" onclick="selectAllStreams()">
                    <i class="fas fa-check-square"></i> Seleccionar Todo
                </button>
                <button class="xbox-btn danger btn-small" onclick="deleteSelectedStreams()" disabled id="delete-selected-btn">
                    <i class="fas fa-trash"></i> Eliminar Seleccionados
                </button>
            </div>
            <table class="results-table">
                <thead>
                    <tr>
                        <th><input type="checkbox" id="select-all-checkbox" onchange="toggleSelectAll()"></th>
                        <th>ID</th>
                        <th>Nombre</th>
                        <th>Tipo</th>
                        <th>Source</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    streams.forEach(stream => {
        html += `
            <tr>
                <td>
                    <input type="checkbox" class="stream-checkbox" value="${stream.id}" 
                           onchange="updateSelectedStreams()">
                </td>
                <td>${stream.id}</td>
                <td>${escapeHtml(stream.stream_display_name || 'Sin nombre')}</td>
                <td>${escapeHtml(stream.type_name || stream.type)}</td>
                <td>${escapeHtml(truncateText(stream.stream_source || 'N/A', 50))}</td>
                <td>
                    <div class="table-actions">
                        <button class="xbox-btn secondary btn-small" onclick="viewStreamDetails(${stream.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="xbox-btn danger btn-small" onclick="deleteStream(${stream.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    resultsPanel.innerHTML = html;
}

/**
 * Limpiar búsqueda
 */
function clearSearch() {
    document.getElementById('search-source').value = '';
    document.getElementById('stream-type').value = '';
    if (document.getElementById('search-display-name')) {
        document.getElementById('search-display-name').value = '';
    }
    
    const resultsPanel = document.getElementById('streams-results');
    resultsPanel.innerHTML = `
        <div class="empty-state">
            <i class="fas fa-search"></i>
            <h3>Buscar Streams</h3>
            <p>Utiliza los filtros de arriba para buscar streams en la base de datos</p>
        </div>
    `;
    
    selectedStreams = [];
}

/**
 * Actualizar streams seleccionados
 */
function updateSelectedStreams() {
    const checkboxes = document.querySelectorAll('.stream-checkbox:checked');
    selectedStreams = Array.from(checkboxes).map(cb => parseInt(cb.value));
    
    const deleteBtn = document.getElementById('delete-selected-btn');
    if (deleteBtn) {
        deleteBtn.disabled = selectedStreams.length === 0;
    }
    
    // Actualizar checkbox "seleccionar todo"
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const allCheckboxes = document.querySelectorAll('.stream-checkbox');
    
    if (selectAllCheckbox && allCheckboxes.length > 0) {
        selectAllCheckbox.checked = selectedStreams.length === allCheckboxes.length;
        selectAllCheckbox.indeterminate = selectedStreams.length > 0 && selectedStreams.length < allCheckboxes.length;
    }
}

/**
 * Toggle seleccionar todo
 */
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const streamCheckboxes = document.querySelectorAll('.stream-checkbox');
    
    streamCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    
    updateSelectedStreams();
}

/**
 * Seleccionar todos los streams
 */
function selectAllStreams() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    if (selectAllCheckbox) {
        selectAllCheckbox.checked = true;
        toggleSelectAll();
    }
}

/**
 * Eliminar streams seleccionados
 */
function deleteSelectedStreams() {
    if (selectedStreams.length === 0) {
        showNotification('No hay streams seleccionados', 'warning');
        return;
    }
    
    showConfirmationDialog(
        'Confirmar Eliminación',
        `¿Estás seguro de que quieres eliminar ${selectedStreams.length} streams seleccionados?`,
        'Esta acción no se puede deshacer.',
        () => {
            performDeleteStreams(selectedStreams);
        }
    );
}

/**
 * Eliminar un stream individual
 */
function deleteStream(streamId) {
    showConfirmationDialog(
        'Confirmar Eliminación',
        '¿Estás seguro de que quieres eliminar este stream?',
        'Esta acción no se puede deshacer.',
        () => {
            performDeleteStreams([streamId]);
        }
    );
}

/**
 * Realizar eliminación de streams
 */
async function performDeleteStreams(streamIds) {
    try {
        const response = await fetch('api/streams.php?action=delete_streams', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                stream_ids: streamIds,
                confirm: true
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showNotification(data.message, 'success');
            // Recargar resultados
            searchStreams();
            selectedStreams = [];
        } else {
            throw new Error(data.message);
        }
        
    } catch (error) {
        console.error('Error al eliminar streams:', error);
        showNotification('Error al eliminar streams: ' + error.message, 'error');
    }
}

/**
 * Funciones de utilidad
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

/**
 * Mostrar notificación
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${getNotificationIcon(type)}"></i>
        ${message}
    `;

    document.body.appendChild(notification);

    // Mostrar notificación
    setTimeout(() => notification.classList.add('show'), 100);

    // Ocultar después de 5 segundos
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 5000);
}

function getNotificationIcon(type) {
    switch (type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        default: return 'info-circle';
    }
}

/**
 * Mostrar diálogo de confirmación
 */
function showConfirmationDialog(title, message, warning, onConfirm) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="modal-close" onclick="closeModal(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="confirmation-dialog">
                    <div class="warning-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3>${message}</h3>
                    <p>${warning}</p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="xbox-btn secondary" onclick="closeModal(this)">
                    <i class="fas fa-times"></i> Cancelar
                </button>
                <button class="xbox-btn danger" onclick="confirmAction(this)">
                    <i class="fas fa-check"></i> Confirmar
                </button>
            </div>
        </div>
    `;

    // Guardar callback en el modal
    modal._onConfirm = onConfirm;

    document.body.appendChild(modal);
    setTimeout(() => modal.classList.add('active'), 10);
}

/**
 * Cerrar modal
 */
function closeModal(element) {
    const modal = element.closest('.modal-overlay');
    modal.classList.remove('active');
    setTimeout(() => modal.remove(), 300);
}

/**
 * Confirmar acción en modal
 */
function confirmAction(element) {
    const modal = element.closest('.modal-overlay');
    if (modal._onConfirm) {
        modal._onConfirm();
    }
    closeModal(element);
}
